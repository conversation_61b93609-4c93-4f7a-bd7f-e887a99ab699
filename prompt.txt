Prompt for Coding Agent: Compression Field Theory Validation on MNIST Autoencoder
Objective
Rigorously test the following field-theoretic hypotheses on an autoencoder trained to compress MNIST images:

Field Existence: The distortion and complexity vector fields, as precisely defined below, can be measured and computed in the latent space.

Field Interaction: These fields exhibit meaningful interaction—i.e., the dynamics and geometry of one field affect, and are affected by, the other.

Anti-Alignment Conjecture: As the autoencoder becomes an efficient compressor through training, the distortion and complexity fields in the latent space approach local anti-alignment (dot product approaches -1) where both field magnitudes are high.

Training Dynamics: This anti-alignment emerges as a function of training time and correlates with the model’s approach to optimal compression (minimum description length regime).

Experimental Setup
Dataset and Model
Use MNIST grayscale images (provided).

Build an autoencoder (fully connected or convolutional, specify architecture) with a small latent space (2D or 3D for visualization, possibly 8D for richer validation).

Include explicit regularization (weight decay, VAE KL divergence, or bottleneck) to encourage compression.

Save model checkpoints throughout training (e.g., every 5 or 10 epochs).

Field Definitions (Latent Space)
Distortion at latent code 
𝑧
z: 
𝐷
(
𝑧
)
=
∥
𝑥
−
𝑥
^
(
𝑧
)
∥
2
D(z)=∥x− 
x
^
 (z)∥ 
2
 , where 
𝑥
^
(
𝑧
)
x
^
 (z) is the decoded image.

Distortion Gradient Field: 
𝑑
⃗
(
𝑧
)
=
∇
𝑧
𝐷
(
𝑧
)
d
 (z)=∇ 
z
​
 D(z)

Complexity at 
𝑧
z:
Use either:

the squared norm of decoder Jacobian w.r.t. 
𝑧
z (i.e., 
∥
∇
𝑧
𝑥
^
(
𝑧
)
∥
2
∥∇ 
z
​
  
x
^
 (z)∥ 
2
 ), or

the squared norm of decoder parameter sensitivity (i.e., 
∥
∇
𝜃
𝑥
^
(
𝑧
)
∥
2
∥∇ 
θ
​
  
x
^
 (z)∥ 
2
 ).

Complexity Gradient Field: 
𝑐
⃗
(
𝑧
)
=
∇
𝑧
𝐶
(
𝑧
)
c
 (z)=∇ 
z
​
 C(z)

All fields and their gradients must be computed using autograd.

Field Sampling and Recording
For a large random sample of MNIST test images:

Encode each 
𝑥
x to latent code 
𝑧
∗
z 
∗
 .

Sample a local grid or ball around 
𝑧
∗
z 
∗
  in latent space (for field measurement and gradient calculation).

For each 
𝑧
z in this neighborhood:

Compute and record: 
𝐷
(
𝑧
)
D(z), 
𝐶
(
𝑧
)
C(z), 
𝑑
⃗
(
𝑧
)
d
 (z), 
𝑐
⃗
(
𝑧
)
c
 (z), norms, dot product (
𝑑
⃗
⋅
𝑐
⃗
d
 ⋅ 
c
 ), angle, and image reconstructions.

Repeat at multiple training checkpoints.

Analysis
Field Existence:

Visualize and tabulate the magnitude, direction, and continuity of 
𝑑
⃗
(
𝑧
)
d
 (z) and 
𝑐
⃗
(
𝑧
)
c
 (z) in latent space.

Confirm that fields are well-defined, smooth, and meaningful.

Field Interaction:

Statistically analyze the local correlation and spatial relationships between the two fields at each checkpoint.

Test for local patterns—do regions of high distortion norm coincide with regions of high complexity norm?

Visualize joint distributions and local field geometry.

Anti-Alignment Conjecture:

In regions where both 
∥
𝑑
⃗
(
𝑧
)
∥
∥ 
d
 (z)∥ and 
∥
𝑐
⃗
(
𝑧
)
∥
∥ 
c
 (z)∥ are large, compute the cosine similarity and dot product.

Report histograms and means of alignment (
cos
⁡
(
𝜃
)
cos(θ)) across the latent space.

Explicitly track the change in average alignment in high-magnitude regions as training progresses.

Training-Time Evolution:

For each checkpoint:

Repeat all field statistics and plots.

Show the trajectory of mean and distribution of field alignment.

Summarize how, when, and where the fields approach anti-alignment (dot product 
→
→ -1).

Compression Diagnostics:

Relate the emergence of anti-alignment to:

Validation loss (reconstruction error)

Estimated model description length (if using VAE/MDL or via bits-back estimate)

Visual quality of reconstructions.

Reporting and Deliverables
Well-documented code for training, field computation, analysis, and visualization.

Figures:

Quiver/stream plots of both fields at multiple epochs.

Heatmaps of alignment, field norms, and joint density.

Plots tracking alignment statistics over time.

Statistical tables with field alignment metrics, region statistics, and correlation coefficients.

Comprehensive markdown report that:

Interprets the existence, interaction, and alignment results.

Explains whether the data support the anti-alignment theory and under what circumstances.

Discusses any deviations, special cases, and limitations.

Makes recommendations for future modeling or diagnostic field-theoretic metrics in neural compression.

Raw data and figures saved for reproduction and further analysis.

Goal:
Validate or refute the core field-theoretic hypothesis:
Are distortion and complexity vector fields truly present, do they interact as hypothesized, and does anti-alignment robustly emerge as the model approaches optimal compression?